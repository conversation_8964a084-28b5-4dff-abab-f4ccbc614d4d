/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Nunito', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Header Styles */
.game-header {
    text-align: center;
    margin-bottom: 30px;
    animation: slideInDown 0.8s ease-out;
}

.game-title {
    font-family: 'Fredoka One', cursive;
    font-size: 2.5rem;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 10px;
    animation: bounce 2s infinite;
}

.game-title i {
    margin: 0 15px;
    color: #ffd700;
    animation: pulse 2s infinite;
}

.game-subtitle {
    font-size: 1.2rem;
    color: #f0f0f0;
    font-weight: 600;
}

/* Game Controls */
.game-controls {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
    justify-content: center;
    animation: slideInUp 0.8s ease-out;
}

.difficulty-selector, .theme-selector {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.difficulty-selector label, .theme-selector label {
    color: #fff;
    font-weight: 600;
    font-size: 0.9rem;
}

.difficulty-dropdown, .theme-dropdown {
    padding: 10px 15px;
    border: none;
    border-radius: 25px;
    background: rgba(255,255,255,0.9);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.difficulty-dropdown:hover, .theme-dropdown:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.btn:active {
    transform: translateY(-1px);
}

/* Scoreboard */
.scoreboard {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    animation: slideInLeft 0.8s ease-out;
}

.score-item {
    background: rgba(255,255,255,0.95);
    padding: 20px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    min-width: 100px;
}

.score-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.score-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 600;
    margin-bottom: 5px;
}

.score-value {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.score-icon {
    font-size: 1.5rem;
}

/* Game Status */
.game-status {
    text-align: center;
    margin-bottom: 30px;
    animation: slideInRight 0.8s ease-out;
}

.game-message {
    font-size: 1.3rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.turn-indicator {
    display: flex;
    justify-content: center;
    gap: 30px;
}

.player-turn, .ai-turn {
    padding: 10px 20px;
    border-radius: 20px;
    background: rgba(255,255,255,0.2);
    color: #fff;
    font-weight: 600;
    transition: all 0.3s ease;
    opacity: 0.5;
}

.player-turn.active, .ai-turn.active {
    opacity: 1;
    background: rgba(255,255,255,0.9);
    color: #333;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Game Board */
.game-board-container {
    position: relative;
    margin-bottom: 40px;
    animation: zoomIn 0.8s ease-out;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 8px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.cell {
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.cell:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    background: rgba(255,255,255,1);
}

.cell.taken {
    cursor: not-allowed;
}

.cell.taken:hover {
    transform: none;
}

.cell.x {
    color: #ff6b6b;
    animation: popIn 0.5s ease-out;
}

.cell.o {
    color: #4ecdc4;
    animation: popIn 0.5s ease-out;
}

/* Winning Line */
.winning-line {
    position: absolute;
    background: #ffd700;
    border-radius: 5px;
    opacity: 0;
    transition: all 0.5s ease;
    z-index: 10;
    box-shadow: 0 0 20px #ffd700;
}

.winning-line.show {
    opacity: 1;
    animation: drawLine 0.8s ease-out;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 10% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: slideInDown 0.5s ease-out;
    overflow: hidden;
}

.modal-header {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.modal-header h2 {
    color: #fff;
    font-family: 'Fredoka One', cursive;
    font-size: 1.8rem;
}

.modal-close {
    color: #fff;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-close:hover {
    transform: scale(1.2);
    color: #ff6b6b;
}

.modal-body {
    padding: 30px;
    text-align: center;
    color: #fff;
}

.modal-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: bounce 1s infinite;
}

.modal-body p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    font-weight: 600;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Instructions */
.instructions {
    background: rgba(255,255,255,0.95);
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    animation: slideInUp 0.8s ease-out;
}

.instructions h3 {
    color: #333;
    margin-bottom: 15px;
    font-family: 'Fredoka One', cursive;
}

.instructions ul {
    list-style: none;
    padding: 0;
}

.instructions li {
    margin-bottom: 10px;
    font-weight: 600;
    color: #555;
}

/* Footer */
.game-footer {
    text-align: center;
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
    margin-top: auto;
}

/* Animations */
@keyframes slideInDown {
    from { transform: translateY(-100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInUp {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes zoomIn {
    from { transform: scale(0.5); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes popIn {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes drawLine {
    from { transform: scaleX(0); }
    to { transform: scaleX(1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container { padding: 15px; }
    .game-title { font-size: 2rem; }
    .game-controls { flex-direction: column; align-items: center; }
    .scoreboard { flex-direction: column; gap: 15px; }
    .turn-indicator { flex-direction: column; gap: 10px; }
    .cell { width: 80px; height: 80px; font-size: 2rem; }
    .modal-content { margin: 20% auto; width: 95%; }
}

@media (max-width: 480px) {
    .game-title { font-size: 1.5rem; }
    .cell { width: 70px; height: 70px; font-size: 1.8rem; }
    .scoreboard { gap: 10px; }
    .score-item { padding: 15px; min-width: 80px; }
}

/* Theme Variations */
body.theme-ocean {
    background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
}

body.theme-forest {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

body.theme-sunset {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

body.theme-space {
    background: linear-gradient(135deg, #2c3e50 0%, #4a6741 100%);
}

/* Theme-specific modal backgrounds */
body.theme-ocean .modal-content {
    background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
}

body.theme-forest .modal-content {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

body.theme-sunset .modal-content {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

body.theme-space .modal-content {
    background: linear-gradient(135deg, #2c3e50 0%, #4a6741 100%);
}

/* AI Thinking Animation */
.ai-thinking {
    animation: thinking 1.5s infinite;
}

@keyframes thinking {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Winning Cell Highlight */
.cell.winning {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    animation: winningGlow 1s infinite alternate;
}

@keyframes winningGlow {
    from { box-shadow: 0 0 20px #ffd700; }
    to { box-shadow: 0 0 40px #ffd700, 0 0 60px #ffd700; }
}
